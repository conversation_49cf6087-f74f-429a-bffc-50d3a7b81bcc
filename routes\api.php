<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\InspectorProfileController;
use App\Http\Controllers\Api\NotificationApiController;
use App\Http\Controllers\Api\AssignedProjectApiController;
use App\Http\Controllers\Api\PhysicalInspectionReportApi;
use App\Http\Controllers\Api\EmailMessageApiController;
use App\Http\Controllers\Api\GuidlineController;
use App\Http\Controllers\Api\ShareController;
use App\Http\Controllers\Api\AnnotatedPdfController;


Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('verify-otp', [AuthController::class, 'verifyOtp']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
         Route::post('resend-otp', [AuthController::class, 'resendOtp'])->middleware('throttle:5,1');
         Route::post('change-password', [AuthController::class, 'changePassword'])->middleware('auth:sanctum');
        // Route::post('reset-password', [AuthController::class, 'resetPassword'])->middleware('auth:sanctum');

    Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');

});
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/profile', [InspectorProfileController::class, 'getProfile'])->name('profile.get');
    Route::delete('/profile-delete', [InspectorProfileController::class, 'deleteProfile'])->name('profile.delete');
    Route::post('/profile-update', [InspectorProfileController::class, 'updateProfile'])->name('profile.update');
});
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/notifications', [NotificationApiController::class, 'index'])->name('api.notifications.index');
    Route::get('/notifications/{notification}', [NotificationApiController::class, 'show'])->name('api.notifications.show');
});
Route::get('assigned-projects', [AssignedProjectApiController::class, 'index'])->middleware('auth:sanctum');
Route::get('assigned-projects/count', [AssignedProjectApiController::class, 'count'])->middleware('auth:sanctum');
Route::post('/inspectors-create', [InspectorProfileController::class, 'createInspector'])->name('inspectors.create');
Route::get('/inspectors-list', [InspectorProfileController::class, 'getInspectors'])->name('inspectors.get');

Route::post('/physical-inspection-reports', [PhysicalInspectionReportApi::class, 'store'])->middleware('auth:sanctum');
Route::get('/physical-inspection-reports-list', [PhysicalInspectionReportApi::class, 'index'])->middleware('auth:sanctum');
Route::get('assigned-projects/{id}', [AssignedProjectApiController::class, 'show']);
Route::get('/activeInspections', [PhysicalInspectionReportApi::class, 'activeInspection'])->middleware('auth:sanctum');
Route::get('/count-pending', [PhysicalInspectionReportApi::class, 'countPending'])->name('api.physicalinspection.countPending');
  Route::get('/physical-inspection/download', [PhysicalInspectionReportApi::class, 'downloadReport'])->name('api.physicalinspection.download');
  Route::get('/count-violations', [PhysicalInspectionReportApi::class, 'countViolations'])->name('api.physicalinspection.countViolations');
  Route::get('projects/{project_id}/details', [PhysicalInspectionReportApi::class, 'getProjectDetails']);
    Route::get('/inspection/count-all', [PhysicalInspectionReportApi::class, 'countAll']);
    Route::middleware('auth:api')->post('/notifications/read-all', [NotificationApiController::class, 'markAllAsRead']);
Route::middleware('auth:api')->delete('/notifications/{id}', [NotificationApiController::class, 'delete']);
//messages
Route::middleware('auth:api')->group(function () {
    Route::get('/all-user-count', [EmailMessageApiController::class, 'index'])->name('api.messages.index');
    Route::get('/engineer', [EmailMessageApiController::class, 'architects'])->name('api.architects.index');
    Route::post('/engineer/message', [EmailMessageApiController::class, 'storeEngineer'])->name('api.engineer.store');
    Route::get('/builders', [EmailMessageApiController::class, 'builders'])->name('api.builders');
    Route::post('/builders/message', [EmailMessageApiController::class, 'storeBuilder'])->name('api.storeBuilder');
    Route::get('/mcd', [EmailMessageApiController::class, 'mcd'])->name('api.mcd');
    Route::post('/mcd/message', [EmailMessageApiController::class, 'storeMcd'])->name('api.storeMcd');
     Route::get('/inspectors', [EmailMessageApiController::class, 'inspectors'])->name('api.inspectors');
    Route::post('/inspectors/message', [EmailMessageApiController::class, 'storeInspector'])->name('api.storeInspector');
    Route::post('/messages', [EmailMessageApiController::class, 'store'])->name('api.messages.store');
});
Route::middleware('auth:api')->group(function () {
    Route::get('/messages/inbox', [EmailMessageApiController::class, 'inbox'])->name('api.messages.inbox');
    Route::get('/messages/outbox', [EmailMessageApiController::class, 'outbox'])->name('api.messages.outbox');
    Route::get('/messages/starred', [EmailMessageApiController::class, 'starred'])->name('api.messages.starred');
    Route::get('/messages/trash', [EmailMessageApiController::class, 'trash'])->name('api.messages.trash');
Route::post('/messages/star/{message_id}', [EmailMessageApiController::class, 'star']);
    Route::delete('/messages/{id}', [EmailMessageApiController::class, 'destroy']);
    Route::delete('/trashAll', [EmailMessageApiController::class, 'deleteAllTrashedMessages'])->name('messages.deleteAllTrashed');
Route::get('/messages/{id}', [EmailMessageApiController::class, 'showMessageDetail']);
});
//share
Route::get('/buildersshare', [ShareController::class, 'builders'])->name('api.builders');
Route::get('/architectsshare', [ShareController::class, 'architects'])->name('api.architects');
Route::post('/send-to-builder', [ShareController::class, 'sendToBuilder'])->name('api.send-to-builder');
Route::post('/send-to-architect', [ShareController::class, 'sendToArchitect'])->name('api.send-to-architect');

Route::get('/guidelines', [GuidlineController::class, 'index']);
Route::get('/guidelines/{id}', [GuidlineController::class, 'show']);

// Annotated PDF routes - no CSRF protection needed for API routes
Route::post('/annotated-pdfs', [AnnotatedPdfController::class, 'store']);
Route::get('/projects/{project_id}/annotated-pdfs', [AnnotatedPdfController::class, 'getProjectAnnotatedPdfs']);
Route::get('/projects/{project_id}/annotations', [AnnotatedPdfController::class, 'getProjectAnnotations']);
Route::get('/download-annotated-pdf', [AnnotatedPdfController::class, 'downloadAnnotatedPdf']);

// Node.js service communication route
Route::post('/call-node-service', function () {
    $response = Http::post('http://localhost:3001/process', [
        'message' => 'Hello from Laravel!',
        'timestamp' => now()->toISOString(),
        'user_id' => auth()->id() ?? 'guest'
    ]);
    
    return response()->json([
        'success' => true,
        'node_response' => $response->json(),
        'laravel_message' => 'Successfully communicated with Node.js service'
    ]);
});
