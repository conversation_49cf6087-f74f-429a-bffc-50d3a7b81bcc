<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Inspector;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\File;
use App\Models\McdStaffRole;


class InspectorProfileController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }
    // public function getProfile(Request $request)
    // {
    //     $inspector = $request->user();

    //     if (!$inspector) {
    //         return response()->json(['message' => 'Unauthorized'], 401);
    //     }

    //     return response()->json([
    //         'message' => 'Profile retrieved successfully',
    //         'inspector' => $inspector,
    //     ], 200);
    // }
    public function getProfile(Request $request)
{
    //dd('jjj');
    $inspector = $request->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    // Eager load the role relationship to get the department name
    $inspector->load('role');

    return response()->json([
        'message' => 'Profile retrieved successfully',
        'inspector' => [
            'first_name' => $inspector->first_name,
            'last_name' => $inspector->last_name,
            'email' => $inspector->email,
            'phone' => $inspector->phone,
            'designation' => $inspector->designation,
            'department' => $inspector->role ? $inspector->role->name : null, // Get department name
            'address' => $inspector->address,
            'status' => $inspector->status,
            'inspector_id' => $inspector->inspector_id,
            'image' => $inspector->image,
        ],
    ], 200);
}

    public function updateProfile(Request $request)
    {
        $inspector = $request->user();

        if (!$inspector) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $request->validate([
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'email' => 'required|email|unique:inspectors,email,' . $inspector->id,
            'phone' => 'required|string|max:15|regex:/^[0-9\-\+\(\)]+$/',
            'password' => 'nullable|string|min:8',
            'designation' => 'nullable|string|max:50',
            'department' => 'nullable|string|max:50',
            'address' => 'nullable|string|max:500',
            'status' => 'nullable|in:active,inactive',
            'inspector_id' => 'nullable|string|max:50|unique:inspectors,inspector_id,' . $inspector->id,
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->except('password', 'image');
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        if ($request->hasFile('image')) {
            // Delete old image
            if ($inspector->image && File::exists(public_path(parse_url($inspector->image, PHP_URL_PATH)))) {
                File::delete(public_path(parse_url($inspector->image, PHP_URL_PATH)));
            }

            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $destinationPath = public_path('inspector');

            if (!File::exists($destinationPath)) {
                File::makeDirectory($destinationPath, 0755, true);
            }

            $file->move($destinationPath, $filename);
            $data['image'] = url('public/inspector/' . $filename);
        }

        $inspector->update($data);

        return response()->json([
            'message' => 'Profile updated successfully',
            'inspector' => $inspector,
        ], 200);
    }

    // public function deleteProfile(Request $request)
    // {
    //     $inspector = $request->user();

    //     if (!$inspector) {
    //         return response()->json(['message' => 'Unauthorized'], 401);
    //     }

    //     if ($inspector->image && File::exists(public_path(parse_url($inspector->image, PHP_URL_PATH)))) {
    //         File::delete(public_path(parse_url($inspector->image, PHP_URL_PATH)));
    //     }

    //     $inspector->delete();

    //     return response()->json(['message' => 'Profile deleted successfully'], 200);
    // }
    public function deleteProfile(Request $request)
{
    $inspector = $request->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    // Validate the password input
    $request->validate([
        'password' => 'required|string',
    ]);

    // Check if the provided password is correct
    if (!Hash::check($request->password, $inspector->password)) {
        return response()->json(['message' => 'Incorrect password'], 403);
    }

    // Delete the image if it exists
    if ($inspector->image && File::exists(public_path(parse_url($inspector->image, PHP_URL_PATH)))) {
        File::delete(public_path(parse_url($inspector->image, PHP_URL_PATH)));
    }

    // Delete the inspector profile
    $inspector->delete();

    return response()->json(['message' => 'Profile deleted successfully'], 200);
}

    public function getInspectors()
    {
        $inspectors = Inspector::latest()->get();
        return response()->json([
            'message' => 'Inspectors retrieved successfully',
            'inspectors' => $inspectors,
        ], 200);
    }

    public function createInspector(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'email' => 'required|email|unique:inspectors,email',
            'password' => 'required|string|min:8',
            'phone' => 'required|string|max:15|regex:/^[0-9\-\+\(\)]+$/',
            'designation' => 'required|string|max:100',
            'department' => 'required|string|max:100',
            'address' => 'required|string|max:500',
            'status' => 'required|in:active,inactive',
            'inspector_id' => 'nullable|string|max:50|unique:inspectors,inspector_id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->except('password', 'image');
        $data['password'] = Hash::make($request->password);

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $destinationPath = public_path('inspector');

            if (!File::exists($destinationPath)) {
                File::makeDirectory($destinationPath, 0755, true);
            }

            $file->move($destinationPath, $filename);
            $data['image'] = url('public/inspector/' . $filename);
        }

        $inspector = Inspector::create($data);

        return response()->json([
            'message' => 'Inspector created successfully',
            'inspector' => $inspector,
        ], 201);
    }
}
